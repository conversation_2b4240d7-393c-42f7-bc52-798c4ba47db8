import Link from 'next/link';

export default function DevToolsPage() {
  const devTools = [
    { name: 'Debug', path: '/dev-tools/debug' },
    { name: 'Test RLS', path: '/dev-tools/test-rls' },
    { name: 'Test Subscription', path: '/dev-tools/test-subscription' },
    { name: 'Test Subscription API', path: '/dev-tools/test-subscription-api' },
  ];

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Development Tools</h1>
      <ul>
        {devTools.map((tool) => (
          <li key={tool.path} className="mb-2">
            <Link href={tool.path} className="text-blue-500 hover:underline">
              {tool.name}
            </Link>
          </li>
        ))}
      </ul>
    </div>
  );
}