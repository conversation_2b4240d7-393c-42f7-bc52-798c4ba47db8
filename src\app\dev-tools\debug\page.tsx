'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';

interface EnvInfo {
  [key: string]: string;
}

interface SupabaseTestResult {
  success: boolean;
  message: string;
  connectionTest?: any;
  insertData?: any;
  checkData?: any;
  cleanupSuccess?: boolean;
  error?: string;
  details?: any;
}

export default function DebugPage() {
  const { data: session, status } = useSession();

  const [clientEnv, setClientEnv] = useState<EnvInfo | null>(null);
  const [serverEnv, setServerEnv] = useState<any>(null);
  const [supabaseResult, setSupabaseResult] = useState<SupabaseTestResult | null>(null);
  const [serviceKeyTest, setServiceKeyTest] = useState<any>(null);
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);

        const [clientEnvRes, serverEnvRes, supabaseRes] = await Promise.all([
          fetch('/api/debug/env/client').then(r => r.json()),
          fetch('/api/debug/server-env').then(r => r.json()),
          fetch('/api/debug/supabase-test').then(r => r.json()),
        ]);

        setClientEnv(clientEnvRes);
        setServerEnv(serverEnvRes);
        setSupabaseResult(supabaseRes);

        if (status === 'authenticated') {
          const usersRes = await fetch('/api/v1/users');
          const usersData = await usersRes.json();
          setUsers(usersData.users || []);
        }
      } catch (err: any) {
        console.error(err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [status]);


  async function testServiceKey() {
    try {
      const res = await fetch('/api/debug/service-key-test');
      const data = await res.json();
      setServiceKeyTest(data);
    } catch (err: any) {
      alert(err.message);
    }
  }

  return (
    <div className="max-w-5xl mx-auto p-6 space-y-8">
      <h1 className="text-3xl font-bold">Debug Dashboard</h1>

      {loading && <p>Loading...</p>}
      {error && <p className="text-red-500">Error: {error}</p>}

      {/* Environment Variables */}
      <section>
        <h2 className="text-2xl font-semibold mb-2">Environment Variables</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 border rounded">
            <h3 className="font-semibold mb-2">Client Environment</h3>
            {clientEnv ? (
              <ul className="space-y-1">
                {Object.entries(clientEnv).map(([k, v]) => (
                  <li key={k} className="flex justify-between">
                    <span className="font-mono">{k}</span>
                    <span className={v === 'Set' ? 'text-green-600' : 'text-red-600'}>{v}</span>
                  </li>
                ))}
              </ul>
            ) : <p>No data</p>}
          </div>
          <div className="p-4 border rounded">
            <h3 className="font-semibold mb-2">Server Environment</h3>
            {serverEnv ? (
              <ul className="space-y-1 text-sm">
                <li>Node Env: {serverEnv.nodeEnv}</li>
                <li>Supabase URL: {serverEnv.supabaseUrl.set ? 'Set' : 'Not Set'}</li>
                <li>Service Key: {serverEnv.supabaseServiceKey.set ? 'Set' : 'Not Set'}</li>
                <li>NextAuth URL: {serverEnv.nextAuthUrl.set ? 'Set' : 'Not Set'}</li>
                <li>NextAuth Secret: {serverEnv.nextAuthSecret.set ? 'Set' : 'Not Set'}</li>
              </ul>
            ) : <p>No data</p>}
          </div>
        </div>
      </section>

      {/* Supabase Test */}
      <section>
        <h2 className="text-2xl font-semibold mb-2">Supabase Connectivity</h2>
        {supabaseResult ? (
          <div className="p-4 border rounded space-y-2">
            <p>Status: <span className={supabaseResult.success ? 'text-green-600' : 'text-red-600'}>{supabaseResult.message}</span></p>
            {supabaseResult.error && <p className="text-red-500">Error: {supabaseResult.error}</p>}
            <pre className="p-2 rounded overflow-auto text-xs">{JSON.stringify(supabaseResult, null, 2)}</pre>
          </div>
        ) : <p>No data</p>}
      </section>

      {/* Service Key Test */}
      <section>
        <h2 className="text-2xl font-semibold mb-2">Service Key Test</h2>
        <button onClick={testServiceKey} className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 mb-2">Run Service Key Test</button>
        {serviceKeyTest && (
          <div className="p-4 border rounded space-y-2">
            <p>{serviceKeyTest.message}</p>
            <pre className="bg-gray-100 p-2 rounded overflow-auto text-xs">{JSON.stringify(serviceKeyTest, null, 2)}</pre>
          </div>
        )}
      </section>

      {/* Users */}
      {status === 'authenticated' && (
        <section>
          <h2 className="text-2xl font-semibold mb-2">Users</h2>
          <div className="overflow-auto">
            <table className="min-w-full border">
              <thead>
                <tr>
                  <th className="border px-2 py-1">ID</th>
                  <th className="border px-2 py-1">Email</th>
                  <th className="border px-2 py-1">Name</th>
                  <th className="border px-2 py-1">Created</th>
                </tr>
              </thead>
              <tbody>
                {users.map((u) => (
                  <tr key={u.id}>
                    <td className="border px-2 py-1 text-xs">{u.id}</td>
                    <td className="border px-2 py-1">{u.email}</td>
                    <td className="border px-2 py-1">{u.name}</td>
                    <td className="border px-2 py-1">{new Date(u.created_at).toLocaleString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </section>
      )}

      {/* Actions */}
      <section>
        <h2 className="text-2xl font-semibold mb-2">Debug Actions</h2>
        <div className="flex flex-wrap gap-2">
          <a href="/api/debug/supabase-test" target="_blank" className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">Run Supabase Test (New Tab)</a>
          <a href="/api/debug/service-key-test" target="_blank" className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700">Run Service Key Test (New Tab)</a>
        </div>
      </section>
    </div>
  );
}
